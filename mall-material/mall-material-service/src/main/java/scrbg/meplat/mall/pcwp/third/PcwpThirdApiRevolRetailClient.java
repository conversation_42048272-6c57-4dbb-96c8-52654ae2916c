package scrbg.meplat.mall.pcwp.third;

import java.util.List;
import java.util.Map;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import scrbg.meplat.mall.pcwp.FeignConfig;
import scrbg.meplat.mall.pcwp.KeyedPayload;
import scrbg.meplat.mall.pcwp.PcwpClient;
import scrbg.meplat.mall.pcwp.PcwpPageRes;
import scrbg.meplat.mall.pcwp.PcwpRes;
import scrbg.meplat.mall.pcwp.dto.PcwpRevolAcceptanceDto;
import scrbg.meplat.mall.pcwp.dto.PcwpSaveSiteReceivingRequest;
import scrbg.meplat.mall.pcwp.third.model.RevolPlan;
import scrbg.meplat.mall.pcwp.third.model.RevolPlanPageQueryCondition;
import scrbg.meplat.mall.pcwp.third.model.RevolPlanPageQueryResult;
import scrbg.meplat.mall.pcwp.third.model.StatePlan;
import scrbg.meplat.mall.pcwp.third.model.UpdatePlanDtl;
/**
 * 第三方接口服务
 * 周转材料相关
 * 还未对接，这里暂时模拟一下
 */
@FeignClient(name = "pcwp-thirdapi-revol-retail-service", url = "${mall.prodPcwp2Url02}", configuration=FeignConfig.class)
@Profile("!mock-pcwp")
public interface PcwpThirdApiRevolRetailClient extends PcwpClient{
    /**
     * 保存推送的周转材料计划(提供给物资采购平台)
     * @param revolRetailPlan
     * @return
     */
    @PostMapping("/thirdapi/turnover/saveTemporaryDemandPlan")
    PcwpRes<String> saveRevolRetailPlan(@RequestBody RevolPlan revolRetailPlan,
                                                @RequestHeader("token") String token, 
                                                @RequestHeader("syscode") String syscode); 
    
    /**
     * 第三方接口服务-根据周转材料计划id获取大周转材料计划(提供给物资采购平台)
     * TODO pcwp未提供此接口
     *
     * @param billId
     * @param token
     * @param sysCode
     * @return
     */
    @GetMapping("/thirdapi/turnover/findDtlByBillId")
    PcwpRes<StatePlan> getRevolRetailPlanById(
            @RequestParam("billId") String billId,
            @RequestHeader("token") String token,
            @RequestHeader("sysCode") String sysCode
    );
    
    /**
     * 反写周转材料计划商城订单数量
     *
     * @param payload
     * @param token
     * @param sysCode
     * @return 
     */
    @PostMapping("thirdapi/turnover/updateShopDtl")
    PcwpRes<Void> updatePlanDtl(
            @RequestBody KeyedPayload<List<UpdatePlanDtl>> payload,
            @RequestHeader("token") String token,
            @RequestHeader("sysCode") String sysCode
    );
    
    /**
     * 分页查询周转材料计划
     *
     * @param filter
     * @param token
     * @param sysCode
     * @return
     */
    @PostMapping("/thirdapi/turnover/queryPageTemporaryDemandPlanDtl")
    PcwpRes<PcwpPageRes<RevolPlanPageQueryResult>> queryPageRevolPlan(
            @RequestBody RevolPlanPageQueryCondition filter,
            @RequestHeader("token") String token,
            @RequestHeader("sysCode") String sysCode
    );

    /**
     * 周才数据回滚 (推送验收,推送计划,反写暂扣及暂扣作废)(物资采购平台)
     * @param keyId
     * @param token
     * @param sysCode
     * @return
     */
    @GetMapping("/thirdapi/turnover/dataRollback")
    PcwpRes<Void> rollbackRevolPlan(
            @RequestParam("keyId") String keyId,
            @RequestHeader("token") String token,
            @RequestHeader("sysCode") String sysCode
    );

    /**
     * 第三方接口服务-检查外部单据是否能作废（提供给物资采购平台）
     *
     * @param billId 验收单id
     * @param orgId  机构id
     * @param token
     * @return 是否可以作废
     */
    @GetMapping("/thirdapi/turnover/checkOutBillCanBeInvalidated")
    PcwpRes<Boolean> checkOutBillCanBeInvalidated(
            @RequestParam("billId") String billId,
            @RequestParam("orgId") String orgId,
            @RequestHeader("token") String token
    );

    /**
     * 保存周转材料验收单(提供给物资采购平台)
     * @param request 周转材料验收单请求对象
     * @param token
     * @return
     */
    @PostMapping("/thirdapi/turnover/saveAcceptance")
    PcwpRes<Void> saveAcceptance(@RequestBody KeyedPayload<PcwpRevolAcceptanceDto> request,
                                   @RequestHeader("token") String token,
                                    @RequestHeader("sysCode") String sysCode);


    @GetMapping("/thirdapi/monthlySettlement/isCanOperaBill")
    PcwpRes<Boolean> isRevolCanOperaBill(@RequestParam("orgId") String orgId,
                                 @RequestParam("date") String date,
                                 @RequestHeader("token") String token,
                                 @RequestHeader("sysCode") String sysCode);
}
