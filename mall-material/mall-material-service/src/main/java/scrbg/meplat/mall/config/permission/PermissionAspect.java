package scrbg.meplat.mall.config.permission;

import com.alibaba.fastjson.JSON;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import scrbg.meplat.mall.common.StringUtils;
import scrbg.meplat.mall.entity.system.SysMenu2;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Set;

@Aspect
@Component
public class PermissionAspect {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    private String getPermissionName(String permissonTag){
        List<SysMenu2> list = JSON.parseArray(stringRedisTemplate.opsForValue().get("sys_menu_permissions"), SysMenu2.class);
        String defaultName = "";
        for(SysMenu2 sysMenu2:list){
            if(permissonTag.equals(sysMenu2.getPerms())){
                defaultName = sysMenu2.getTitle();
                break;
            }
        }
        return defaultName;
    }

    /**
     * 定义切点，拦截所有带有 @RequestPermission 注解的方法
     */
    @Pointcut("execution(* scrbg.meplat.mall.controller..*.*(..)) && @annotation(scrbg.meplat.mall.config.permission.RequestPermission)")
    public void permissionPointcut() {}

    /**
     * 环绕通知，在方法执行前后进行权限校验
     */
    @Around("permissionPointcut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        // 1. 获取方法上的注解
        RequestPermission permissionAnnotation = method.getAnnotation(RequestPermission.class);
        if (permissionAnnotation == null) {
            // 如果没有注解，直接放行
            return point.proceed();
        }
        // 2. 获取注解中定义的所需权限字符串数组
        String[] requiredPermissions = permissionAnnotation.value();
        // 3. 从当前用户会话中获取已有的权限列表
        Set<String> userPermissions = ThreadLocalUtil.getCurrentUser().getPermissions();; // 假设这是一个获取用户权限的工具类方法
        // 4. 进行权限校验
        boolean hasPermission = false;
        String sys_prom = null;
        if (userPermissions != null && !userPermissions.isEmpty()) {
            hasPermission = true;
            for (String requiredPerm : requiredPermissions) {
                if (!userPermissions.contains(requiredPerm)) {
                    sys_prom = getPermissionName(requiredPerm);
                    hasPermission = false; // 只要有一个不包含，就判定为无权
                    break; // 找到一个不匹配的，跳出循环
                }
            }
        }else{
            sys_prom = getPermissionName(requiredPermissions[0]);
        }
        // 5. 判断权限结果
        if (hasPermission) {
            // 权限满足，继续执行方法
            return point.proceed();
        } else {
            // 权限不足，抛出异常，由全局异常处理器处理
            if(StringUtils.isEmpty(sys_prom)){
                throw new Exception("没有对应操作权限，请联系管理员开通");
            }else{
                throw new Exception("没有【"+sys_prom+"】操作权限，请联系管理员开通");
            }
        }
    }
}
