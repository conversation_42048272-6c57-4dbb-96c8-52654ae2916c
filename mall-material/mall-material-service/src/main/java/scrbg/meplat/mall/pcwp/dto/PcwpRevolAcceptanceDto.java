package scrbg.meplat.mall.pcwp.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * PCWP周转材料验收单DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PcwpRevolAcceptanceDto {

    /**
     * 验收明细列表
     */
    private List<AcceptanceDtl> acceptanceDtls;

    /**
     * 验收人ID
     */
    private String acceptancerId;

    /**
     * 验收人姓名
     */
    private String acceptancerName;

    /**
     * 进场费
     */
    private BigDecimal approachCost;

    /**
     * 本位币汇率
     */
    private BigDecimal baseCurExchangeRate;

    /**
     * 本位币ID
     */
    private String baseCurId;

    /**
     * 本位币名称
     */
    private String baseCurName;

    /**
     * 单据ID
     */
    private String billId;

    /**
     * 单据编号
     */
    private String billNo;

    /**
     * 单据来源(1=物资化采购平台生成)
     */
    private Integer billSource;

    /**
     * 业务类型Key
     */
    private String businessTypeKey;

    /**
     * 业务类型Value
     */
    private String businessTypeValue;

    /**
     * 币种名称
     */
    private String currency;

    /**
     * 币种ID
     */
    private String currencyId;

    /**
     * 发票号
     */
    private String invoiceNo;

    /**
     * 是否验收结算一体
     */
    private Integer isAccepSettle;

    /**
     * 采购人ID
     */
    private String purchaserId;

    /**
     * 采购人姓名
     */
    private String purchaserName;

    /**
     * 采购单位ID
     */
    private String purchasingUnitId;

    /**
     * 采购单位名称
     */
    private String purchasingUnitName;

    /**
     * 红单状态
     */
    private Integer redBillState;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 人民币金额
     */
    private BigDecimal rmbAmount;

    /**
     * 人民币汇率
     */
    private BigDecimal rmbRate;

    /**
     * 源单ID
     */
    private String sourceId;

    /**
     * 源单编号
     */
    private String sourceNo;

    /**
     * 状态
     */
    private Integer state;

    /**
     * 供应商ID
     */
    private String supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 税额
     */
    private BigDecimal taxAmount;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 价税合计
     */
    private BigDecimal totalPriceAndTax;

    /**
     * 运输费用(含税)
     */
    private BigDecimal transportationCost;

    /**
     * 验收金额
     */
    private BigDecimal acceptanceAmount;

    /**
     * 验收日期
     */
    private String acceptanceDate;

    /**
     * 验收明细内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AcceptanceDtl {

        /**
         * 验收金额
         */
        private BigDecimal acceptanceAmount;

        /**
         * 验收数量
         */
        private BigDecimal acceptanceQuantity;

        /**
         * 摊销日期
         */
        private Long amortizationDate;

        /**
         * 摊销比例
         */
        private BigDecimal amortizationRatio;

        /**
         * 摊销方式
         */
        private Integer amortizationType;

        /**
         * 物料类别ID
         */
        private String materialClassId;

        /**
         * 物料类别名称
         */
        private String materialClassName;

        /**
         * 物料ID
         */
        private String materialId;

        /**
         * 物料名称
         */
        private String materialName;

        /**
         * 物料单价
         */
        private BigDecimal price;

        /**
         * 分摊运费
         */
        private BigDecimal shareFreight;

        /**
         * 源金额
         */
        private BigDecimal sourceAmount;

        /**
         * 源明细ID
         */
        private String sourceDtlId;

        /**
         * 源数量
         */
        private BigDecimal sourceQuantity;

        /**
         * 规格型号
         */
        private String spec;

        /**
         * 税额
         */
        private BigDecimal taxAmount;

        /**
         * 材质
         */
        private String texture;

        /**
         * 计量单位
         */
        private String unit;

        /**
         * 仓库ID
         */
        private String warehouseId;

        /**
         * 仓库名称
         */
        private String warehouseName;
    }
}
