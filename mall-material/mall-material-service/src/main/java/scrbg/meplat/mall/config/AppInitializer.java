package scrbg.meplat.mall.config;

import com.alibaba.fastjson.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import scrbg.meplat.mall.dto.system.SysMenu2DTO;
import scrbg.meplat.mall.entity.system.SysMenu2;
import scrbg.meplat.mall.service.system.MenuService;

import java.util.List;


/**
 * <AUTHOR>
 * 系统初始化后可以在这里执行一些初始化方法
 *
 * */

@Component
public class AppInitializer implements ApplicationRunner {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private MenuService menuService;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        setMenuCache();//初始化权限缓存
    }

    private void setMenuCache() {
        if(!stringRedisTemplate.hasKey("sys_menu_permissions")){
            List<SysMenu2> list = menuService.getMenuList2(new SysMenu2DTO());
            stringRedisTemplate.opsForValue().set("sys_menu_permissions", JSON.toJSONString(list));
        }
    }
}
