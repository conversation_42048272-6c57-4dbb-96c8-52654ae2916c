package scrbg.meplat.mall.pcwp.third;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.pcwp.FeignConfig;
import scrbg.meplat.mall.pcwp.KeyedPayload;
import scrbg.meplat.mall.pcwp.PcwpClient;
import scrbg.meplat.mall.pcwp.PcwpRes;
import scrbg.meplat.mall.pcwp.dto.PcwpAcceptanceRequest;
import scrbg.meplat.mall.pcwp.dto.PcwpSiteReceiptRequest;
import scrbg.meplat.mall.pcwp.dto.PcwpSaveSiteReceivingRequest;
import scrbg.meplat.mall.pcwp.third.model.ReconciliationDtl;

import java.util.List;
import java.util.Map;

/**
 * 第三方现场收料接口服务
 */
@FeignClient(name = "pcwp-thirdapi-siteReceiving-service", url = "${mall.prodPcwp2Url02}", configuration = FeignConfig.class)
@Profile("!mock-pcwp")
public interface PcwpThirdApiSiteReceivingClient extends PcwpClient {

    /**
     * 1、获取可对账的物资(提供给物资采购平台)
     * 请求示例
     * {
     *   "businessType": 0,--业务类型(1合同2计划3调拨4甲供5暂估6大宗零购计划) false integer
     *   "creditCode": "",--供应商信用代码 false string
     *   "endTime": "",--结束时间 false string
     *   "keyword": "",--模糊查询关键字 false string
     *   "limit": 0,--每页显示条数 false integer
     *   "matterName": "",--物资名称 false string
     *   "orderIds": [],--订单id集合 false array
     *   "orgId": "",--机构id false string
     *   "orgShort": "",--机构简码 false string
     *   "page": 0,--当前页数 false integer
     *   "sourceId": "",--源单id false string
     *   "startTime": "",--开始时间 false string
     *   "state": "",--单据状态 false string
     *   "texture": "" --材质 false string
     * }
     * 响应参数
     * {
     * 	"code": 0,
     * 	"data": {
     * 		"currPage": 0,--当前页数 false integer
     * 		"list": [
     * 			            {
     * 				"billId": "",--收料单id false string
     * 				"billNo": "",--收料单编号 false string
     * 				"dtlId": "",--收料明细id false string
     * 				"factoryPrice": 0,--出厂价 false number
     * 				"fixedFee": 0,--固定价 false number
     * 				"freight": 0,--运费 false number
     * 				"invoiceId": "",--发货单明细id(逗号分隔) false string
     * 				"materialClassId": "",--物资类别id(1级类别id/2级类别id/..) false string
     * 				"materialClassName": "",--物资类别名称(1级类别名称/2级类别名称/..) false string
     * 				"materialId": "",--物资id false string
     * 				"materialName": "",--物资名称 false string
     * 				"networkPrice": 0,--网络价格 false number
     * 				"orderDtlId": "",--订单明细id false string
     * 				"orderId": "",--订单id false string
     * 				"orderNo": "",--发货单编号 false string
     * 				"orgId": "",--机构id false string
     * 				"orgName": "",--机构名称 false string
     * 				"purchasingPersonnelId": "",--采购人员id false string
     * 				"purchasingPersonnelName": "",--采购人员名称 false string
     * 				"quantity": 0,--数量 false number
     * 				"receivingDate": "",--收料单单据日期 false string
     * 				"sourceId": "",--源单id false string
     * 				"sourceName": "",--源单名称 false string
     * 				"sourceNumber": "",--源单编号 false string
     * 				"spec": "",--规格 false string
     * 				"state": "",--单据状态 false string
     * 				"supplierId": "",--供应商id false string
     * 				"supplierName": "",--供应商名称 false string
     * 				"texture": "",--材质 false string
     * 				"totalAmount": 0,--总金额 false number
     * 				"totalQuantity": 0,--总数量 false number
     * 				"tradeId": "",--商品Id(物资贸易平台) false string
     * 				"tradeName": "",--商品名称(物资贸易平台) false string
     * 				"type": 0,--大宗零购计划清单类型(1:浮动价格;2:固定价格) false integer
     * 				"unit": "",--计量单位 false string
     * 				"warehouseId": "",--仓库id false string
     * 				"warehouseName": ""--仓库名称 false string
     *            }
     * 		],
     * 		"pageSize": 0,
     * 		"totalCount": 0,
     * 		"totalPage": 0
     * 	},
     * 	"message": ""
     * }
     * @param request 获取可对账的物资
     * @return 保存结果
     */
    @PostMapping("/thirdapi/siteReceiving/getCanUseSiteReceivingDtl")
    PcwpRes<Map> getCanUseSiteReceivingDtl(@RequestBody PcwpAcceptanceRequest request,
                                           @RequestHeader("token") String token);

    /**
     * 2、回滚保存现场收料(提供给物资采购平台)
     *
     * 响应参数
     * {
     * 	"code": 0,
     * 	"data": {},
     * 	"message": ""
     * }
     *
     * @param keyId 接口调用key
     * @param token
     * @return
     */
    @GetMapping("/thirdapi/siteReceiving/rollBackSaveSiteReceiving")
    PcwpRes<Map> rollBackSaveSiteReceiving(@RequestParam("keyId") String keyId,
                                           @RequestHeader("token") String token);

    /**
     * 3、回滚反写对账单暂扣数量(提供给物资采购平台)
     * 响应参数
     * {
     * 	"code": 0,
     * 	"data": {},
     * 	"message": ""
     * }
     *
     * @param keyId 接口调用key
     * @param token
     * @return
     */
    @GetMapping("/thirdapi/siteReceiving/rollBackWriteBackBillLockQuantiy")
    PcwpRes<Map> rollBackWriteBackBillLockQuantiy(@RequestParam("keyId") String keyId,
                                                  @RequestHeader("token") String token);

    /**
     * 4、回滚反写已审核对账单数量(提供给物资采购平台)
     * 响应参数
     * {
     * 	"code": 0,
     * 	"data": {},
     * 	"message": ""
     * }
     * @param keyId 接口调用key
     * @param token
     * @return
     */
    @GetMapping("/thirdapi/siteReceiving/rollBackWriteBackBillQuantiy")
    PcwpRes<Map> rollBackWriteBackBillQuantiy(@RequestParam("keyId") String keyId,
                                              @RequestHeader("token") String token);

    /**
     * 5、保存现场收料(提供给物资采购平台)
     *请求示例
     * {
     *   "data": { --保存数据 false array
     *     "billDate": "",--单据日期	 false string
     *     "billId": "",--单据ID	 false string
     *     "billNo": "",--单据编号	 false string
     *     "businessType": --用业务类型(1合同2计划3调拨4甲供5暂估6大宗零购计划) 	 false integer
     *     "creditCode": "",--供应商信用代码	 false string
     *     "details": [ --现场收料明细实体集合类 false array
     *       {
     *         "acceptLockQuantity": 0,--验收锁定数量	 false number
     *         "amount": 0,--金额	 false number
     *         "billId": "",--单据ID	 false string
     *         "billLockQuantity": 0,--账单锁定数量	 false number
     *         "dtlId": "",--明细ID	 false string
     *         "factoryPrice": 0,--出厂价	 false number
     *         "fixedFee": 0,--固定费用	 false number
     *         "freight": 0,--运费	 false number
     *         "grossWeight": 0,--毛重	 false number
     *         "invoiceId": "",--发货单id(逗号分隔) false string
     *         "materialClassId": "",--物资类别id(1级类别id/2级类别id/..) false string
     *         "materialClassName": "",--物资类别名称(1级类别名称/2级类别名称/..) false string
     *         "matterId": "",--物资id	 false string
     *         "matterName": "",--物资名称	 false string
     *         "matterUnit": "",--物资单位	 false string
     *         "matterUse": "",--物资用途	 false string
     *         "networkPrice": 0,--网络价格	 false number
     *         "number": 0,--数量	 false number
     *         "orderDtlId": "",--订单明细id	 false string
     *         "orderId": "",--订单id	 false string
     *         "orderNo": "",--发货单编号 false string
     *         "outDtlId": "",--外部系统明细id	 false string
     *         "price": 0,--单价	 false number
     *         "reduceWeight": 0,--减重	 false number
     *         "serialNumber": 0,--序号	 false integer
     *         "sourceDtlId": "",--源单明细id	 false string
     *         "spec": "",--规格	 false string
     *         "supWeight": 0,--送货单重量	 false number
     *         "tare": 0,--皮重	 false number
     *         "texture": "",--材质	 false string
     *         "theoreticalWeight": 0,--理论重量	 false number
     *         "tradeId": "",--商品Id(物资贸易平台)	false string
     *         "tureWeight": 0,--实重	 false number
     *         "wareHouseId": "",--仓库id	 false string
     *         "wareHouseName": ""--仓库名称	 false string
     *       }
     *     ],
     *     "entryFee": 0,--进场费 false number
     *     "isOut": 0,--是否是系统外直接录入的单据 false integer
     *     "orgId": "",--机构id false string
     *     "orgName": "",--机构名称 false string
     *     "orgShort": "",--机构简码 false string
     *     "purchaseId": "",--采购人员id false string
     *     "purchaseName": "",--采购人员名称 false string
     *     "purchaseUnitId": "",--采购单位id false string
     *     "purchaseUnitName": "",--采购单位名称 false string
     *     "receiverId": "",--收料人员id false string
     *     "receiverName": "",--收料人员名称 false string
     *     "recorderId": "",--录入人ID false string
     *     "remark": "",--备注 false string
     *     "sourceId": "",--源单id false string
     *     "sourceNo": "",--源单编号 false string
     *     "state": 0,--状态 false integer
     *     "storageId": "",--供应商id false string
     *     "storageName": "",--供应商名称 false string
     *     "storageOrgId": "",--供应商组织机构id false string
     *     "timeStamp": "",--时间戳 false string
     *     "type": 0 --大宗零购计划清单类型(1:浮动价格;2:固定价格) false integer
     *   },
     *   "keyId": ""--本次操作keyId false string
     * }
     * 响应参数
     * {
     * 	"code": 0,
     * 	"data": {},
     * 	"message": ""
     * }
     *
     * @param request 保存现场收料请求数据
     * @param token
     * @return
     */
    @PostMapping("/thirdapi/siteReceiving/saveSiteReceiving")
    PcwpRes<Map> saveSiteReceiving(@RequestBody PcwpSaveSiteReceivingRequest request,
                                   @RequestHeader("token") String token);

    /**
     * 6、反写对账单暂扣数量(提供给物资采购平台)
     *{
     *   "data": [  --反写数据 false array
     *     {
     *       "amount": 0, --金额 false number
     *       "dtlId": "", --现场收料明细Id false string
     *       "quantity": 0  --数量 false number
     *     }
     *   ],
     *   "keyId": "",--本次操作keyId false string
     *   "orgId": ""  --机构id false string
     * }
     * 响应参数
     * {
     * 	"code": 0,
     * 	"data": {},
     * 	"message": ""
     * }
     * @param payLoad
     * @param token
     * @return
     */
    @PostMapping("/thirdapi/siteReceiving/writeBackBillLockQuantiy")
    PcwpRes<Void> writeBackBillLockQuantiy(@RequestBody KeyedPayload<List<ReconciliationDtl>> payLoad,
                                          @RequestHeader("token") String token);

    /**
     * 7、反写已审核对账单数量(提供给物资采购平台)
     *
     * 请求参数
     * {
     *   "data": [  --反写数据 false array
     *     {
     *       "amount": 0, --金额 false number
     *       "dtlId": "", --现场收料明细Id false string
     *       "quantity": 0  --数量 false number
     *     }
     *   ],
     *   "keyId": "",--本次操作keyId false string
     *   "orgId": ""  --机构id false string
     *
     * }
     * 响应参数
     * {
     * 	"code": 0,
     * 	"data": {},
     * 	"message": ""
     * }
     * @param payLoad 现场收料请求数据
     * @param token
     * @return
     */
    @PostMapping("/thirdapi/siteReceiving/writeBackBillQuantiy")
    PcwpRes<Void> writeBackBillQuantiy(@RequestBody KeyedPayload<List<ReconciliationDtl>> payLoad,
                                              @RequestHeader("token") String token);
}
