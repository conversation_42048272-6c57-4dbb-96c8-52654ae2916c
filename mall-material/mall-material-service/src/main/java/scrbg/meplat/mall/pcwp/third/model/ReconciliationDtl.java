package scrbg.meplat.mall.pcwp.third.model;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 对账明细实体类，用于PCWP反写对账单暂扣数量接口。
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReconciliationDtl {
    
    /**
     * 金额 (number类型)
     */
    private BigDecimal amount;

    /**
     * 现场收料明细Id
     */
    private String dtlId;

    /**
     * 数量 (number类型)
     */
    private BigDecimal quantity;
}
