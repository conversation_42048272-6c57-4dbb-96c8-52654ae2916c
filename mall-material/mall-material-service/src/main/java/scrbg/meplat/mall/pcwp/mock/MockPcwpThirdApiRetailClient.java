package scrbg.meplat.mall.pcwp.mock;

import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import scrbg.meplat.mall.entity.plan.Plan;
import scrbg.meplat.mall.pcwp.KeyedPayload;
import scrbg.meplat.mall.pcwp.PcwpPageRes;
import scrbg.meplat.mall.pcwp.PcwpRes;
import scrbg.meplat.mall.pcwp.third.PcwpThirdApiRetailClient;
import scrbg.meplat.mall.pcwp.third.model.RetailPlan;
import scrbg.meplat.mall.pcwp.third.model.SporadicPurchasePlanEx;
import scrbg.meplat.mall.pcwp.third.model.SporadicPurchasePlanPageQueryCondition;
import scrbg.meplat.mall.pcwp.third.model.SporadicPurchasePlanQueryResult;
import scrbg.meplat.mall.pcwp.third.model.UpdatePlanDtl;
import scrbg.meplat.mall.service.plan.PlanService;
/**
 * mock客户端
 */
@Component
@Profile("mock-pcwp")
@Primary
public class MockPcwpThirdApiRetailClient implements PcwpThirdApiRetailClient{

    @Autowired
    private PlanService planService;

    @Override
    public PcwpRes<String> savePlan(KeyedPayload<RetailPlan> plan, String token, String syscode) {
        return PcwpRes.<String>builder().code(200).data(UUID.randomUUID().toString().replaceAll("-", "")).build();
    }

    @Override
    public PcwpRes<Void> updatePlanDtl(KeyedPayload<List<UpdatePlanDtl>> payload, String token, String sysCode) {
        return PcwpRes.<Void>builder().code(200).build();
    }

    @Override
    public PcwpRes<SporadicPurchasePlanEx> getRetailPlanById(String id, String token, String sysCode) {
        SporadicPurchasePlanEx data = SporadicPurchasePlanEx.builder().state("1").build();
        return PcwpRes.<SporadicPurchasePlanEx>builder().code(200).data(data).build();
    }

    /**
     * 所有计划全部审核通过
     */
    @Override
    public PcwpRes<PcwpPageRes<SporadicPurchasePlanQueryResult>> queryRetailPlansPage(
            SporadicPurchasePlanPageQueryCondition filter, String token, String sysCode) {
        List<SporadicPurchasePlanQueryResult> results;
        if (filter.getPage()==1) {
            List<Plan> plans = planService.lambdaQuery().isNotNull(Plan::getPBillId).eq(Plan::getType, 0).list();
            results = plans.stream().map(p->{
                                                return SporadicPurchasePlanQueryResult.builder()
                                                .billId(p.getPBillId())
                                                .state(1)
                                                .build();
                                            }).collect(Collectors.toList());
        }else{
            results = Collections.emptyList();
        }
        PcwpPageRes<SporadicPurchasePlanQueryResult> pageRes = PcwpPageRes.<SporadicPurchasePlanQueryResult>builder()
                                                                .currPage(1)
                                                                .list(results)
                                                                .totalCount(results.size())
                                                                .build();
        return PcwpRes.<PcwpPageRes<SporadicPurchasePlanQueryResult>>builder()
                .code(200)
                .data(pageRes)
                .build();
    }

    @Override
    public PcwpRes<Void> rollbackPlan(String keyId, String token, String syscode) {
        throw new UnsupportedOperationException("Unimplemented method 'rollbackPlan'");
    }
    
}
