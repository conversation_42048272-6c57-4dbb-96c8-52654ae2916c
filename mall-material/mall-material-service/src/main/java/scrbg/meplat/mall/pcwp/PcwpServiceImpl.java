package scrbg.meplat.mall.pcwp;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.multipart.MultipartFile;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;
import scrbg.meplat.mall.dto.plan.BulkRetailPlanEX;
import scrbg.meplat.mall.entity.MaterialDemand;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.pcwp.auth.PcwpAuthClient;
import scrbg.meplat.mall.pcwp.auth.PcwpOrganizationClient;
import scrbg.meplat.mall.pcwp.auth.PcwpThirdClient;
import scrbg.meplat.mall.pcwp.auth.model.SignUp;
import scrbg.meplat.mall.pcwp.auth.model.SupplierData;
import scrbg.meplat.mall.pcwp.auth.model.SupplierRes;
import scrbg.meplat.mall.pcwp.auth.model.TokenRes;
import scrbg.meplat.mall.pcwp.dto.PcwpAcceptanceRequest;
import scrbg.meplat.mall.pcwp.dto.PcwpRevolAcceptanceDto;
import scrbg.meplat.mall.pcwp.dto.PcwpSaveSiteReceivingRequest;
import scrbg.meplat.mall.pcwp.dto.PcwpSiteReceiptRequest;
import scrbg.meplat.mall.pcwp.org.PcwpOrgClient;
import scrbg.meplat.mall.pcwp.org.model.Org;
import scrbg.meplat.mall.pcwp.third.PcwpThirdApiBulkRetailClient;
import scrbg.meplat.mall.pcwp.third.PcwpThirdApiClient;
import scrbg.meplat.mall.pcwp.third.PcwpThirdApiCloudCenterClient;
import scrbg.meplat.mall.pcwp.third.PcwpThirdApiReconciliationClient;
import scrbg.meplat.mall.pcwp.third.PcwpThirdApiRetailClient;
import scrbg.meplat.mall.pcwp.third.PcwpThirdApiRevolRetailClient;
import scrbg.meplat.mall.pcwp.third.PcwpThirdApiSiteReceivingClient;
import scrbg.meplat.mall.pcwp.third.PcwpThirdApiUploadClient;
import scrbg.meplat.mall.pcwp.third.model.BulkRetailPlanPageQueryCondition;
import scrbg.meplat.mall.pcwp.third.model.BulkRetailPlanPageQueryResult;
import scrbg.meplat.mall.pcwp.third.model.Material;
import scrbg.meplat.mall.pcwp.third.model.MaterialPageDto;
import scrbg.meplat.mall.pcwp.third.model.ReconciliationDtl;
import scrbg.meplat.mall.pcwp.third.model.RetailPlan;
import scrbg.meplat.mall.pcwp.third.model.RevolPlan;
import scrbg.meplat.mall.pcwp.third.model.RevolPlanPageQueryCondition;
import scrbg.meplat.mall.pcwp.third.model.RevolPlanPageQueryResult;
import scrbg.meplat.mall.pcwp.third.model.SporadicPurchasePlanEx;
import scrbg.meplat.mall.pcwp.third.model.SporadicPurchasePlanPageQueryCondition;
import scrbg.meplat.mall.pcwp.third.model.SporadicPurchasePlanQueryResult;
import scrbg.meplat.mall.pcwp.third.model.StatePlan;
import scrbg.meplat.mall.pcwp.third.model.UpdatePlanDtl;
import scrbg.meplat.mall.pcwp.third.model.VerifyPlan;

@Slf4j
@Service
public class PcwpServiceImpl implements PcwpService {

    @Autowired
    private PcwpOrganizationClient pcwpOrganizationClient;
    @Autowired
    private PcwpAuthClient pcwpAuthClient;
    @Autowired
    private PcwpThirdClient pcwpThirdClient;
    @Autowired
    private PcwpOrgClient pcwpOrgClient;
    @Autowired
    private PcwpThirdApiClient pcwpThirdApiClient;
    @Autowired
    private PcwpThirdApiBulkRetailClient pcwpThirdApiBulkRetailClient;
    @Autowired
    private PcwpThirdApiReconciliationClient pcwpThirdApiReconciliationClient;
    @Autowired
    private PcwpThirdApiRetailClient pcwpThirdApiRetailClient;
    @Autowired
    private PcwpThirdApiRevolRetailClient pcwpThirdApiRevolRetailClient;
    @Autowired
    private PcwpThirdApiSiteReceivingClient pcwpThirdApiSiteReceivingClient;
    @Autowired
    private PcwpThirdApiCloudCenterClient pcwpThirdApiCloudCenterClient;
    @Autowired
    private PcwpThirdApiUploadClient pcwpThirdApiUploadClient;


    /**
     * 授权方式(0:手机号验证码|1:邮箱|2:微信|3:QQ|5:帐号密码|6:其他)
     * 目前好像只支持5？
     */
    @Value("${app.pcwp.identity-type}")
    private String identityType = "5";
    /**
     * 系统编码
     */
    @Value("${app.pcwp.sys-code}")
    private String sysCode = "msp";

    private static final String SYS_CODE_PCWP2 = "pcwp2";

    /**
     * org
     */
    @Value("${app.pcwp.org-header}")
    private String orgHeader;
    /**
     * 固定token
     */
    @Value("${mall.thirdApiToken}")
    private String token;
    
    /**
     * 传文件到pcwp需要的bucketName
     */
    @Value("material")
    private String bucketName;

    private ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public PcwpRes<TokenRes> signIn(String account, String password) {
        return pcwpAuthClient.signIn(account, password, identityType, sysCode);
    }

    @Override
    public PcwpRes<Void> changeUserPassword(String userId, String oldPwd, String newPwd, String token) {
        return pcwpOrganizationClient.changeUserPassword(userId, oldPwd, newPwd, orgHeader, sysCode, token);
    }

    @Override
    public PcwpRes<TokenRes> createExternalToken(String phoneNo) {
        return pcwpAuthClient.createExternalToken(phoneNo, sysCode);
    }

    @Override
    public PcwpRes<SupplierRes> getSupplierByCreditCode(String creditCode) {
        return pcwpThirdClient.getSupplierByCreditCode(creditCode, token);
    }

    @Override
    public PcwpRes<Boolean> userOrgSignUp(SignUp signUp) {
        return pcwpAuthClient.userOrgSignUp(signUp);
    }

    @Override
    public PcwpRes<List<Org>> getOrgByUserId(String userId) {
        return pcwpOrgClient.getOrgByUserId(userId);
    }

    @Override
    public PcwpRes<Org> getOrgById(String orgId) {
        return pcwpOrgClient.getOrgById(orgId, orgHeader, sysCode, token);
    }

    @Override
    public PcwpRes<List<String>> getUserHasRoles(String userId, Org org, String token) {
        String orgHeader;
        try {
            orgHeader = objectMapper.writeValueAsString(org);
        } catch (JsonProcessingException e) {
            log.error("", e);
            throw new BusinessException(500, e.getMessage());
        }
        return pcwpOrgClient.getUserHasRoles(org.getOrgId(), userId, orgHeader, sysCode, token);
    }

    @Override
    public PcwpRes<String> saveBulkRetailPlan(KeyedPayload<BulkRetailPlanEX> BulkRetailPlan) {
        return pcwpThirdApiBulkRetailClient.saveBulkRetailPlan(BulkRetailPlan, token, SYS_CODE_PCWP2);
    }

    @Override
    public PcwpRes<Void> rollbackBulkRetailPlan(String keyId) {
        return pcwpThirdApiBulkRetailClient.rollbackBulkRetailPlan(keyId, token, SYS_CODE_PCWP2);
    }

    @Override
    public PcwpRes<PcwpPageRes<BulkRetailPlanPageQueryResult>> queryPageBulkRetailPlan(
            BulkRetailPlanPageQueryCondition filter) {
        return pcwpThirdApiBulkRetailClient.queryPageBulkRetailPlan(filter, token, SYS_CODE_PCWP2);
    }

    @Override
    public PcwpRes<List<MaterialDemand>> findDtlByBillId(String billId) {
        return pcwpThirdApiBulkRetailClient.findDtlByBillId(billId,token, SYS_CODE_PCWP2);
    }

    @Override
    public PcwpRes<Void> updateBulkRetailPlanDtl(KeyedPayload<List<UpdatePlanDtl>> payload){
        return pcwpThirdApiBulkRetailClient.updateBulkRetailPlanDtl(payload, token, SYS_CODE_PCWP2);
    }

    @Override
    public PcwpPageRes<Material> queryPageMaterialDtl(MaterialPageDto materialPageDto) {
        // 根据前端代码 /PCWP2/thirdapi/ 开头的url请求header里都要添加syscode=pcwp2
        return pcwpThirdApiClient.queryPageMaterialDtl(materialPageDto, token, SYS_CODE_PCWP2);
    }

    @Override
    public PcwpRes<Boolean> verifyPlan(VerifyPlan verifyPlan) {
        return pcwpThirdApiBulkRetailClient.verifyPlan(verifyPlan, token, SYS_CODE_PCWP2);
    }

    @Override
    public PcwpRes<Boolean> isCanOperaBill(String orgId, String date) {
        return pcwpThirdApiReconciliationClient.isCanOperaBill(orgId, date, token);
    }

    @Override
    public PcwpRes<Boolean> isRevolCanOperaBill(String orgId, String date) {
        return pcwpThirdApiRevolRetailClient.isRevolCanOperaBill(orgId, date, token, SYS_CODE_PCWP2);
    }

    @Override
    public PcwpRes<String> saveAcceptance(PcwpAcceptanceRequest request) {
        return pcwpThirdApiReconciliationClient.saveAcceptance(request, token);
    }

    @Override
    public PcwpRes<Boolean> clearRelationId(String relationId, String orgId) {
        return pcwpThirdApiReconciliationClient.clearRelationId(relationId, orgId, token);
    }

    @Override
    public PcwpRes<Boolean> checkOutBillCanBeInvalidated(String billId, String orgId) {
        return pcwpThirdApiReconciliationClient.checkOutBillCanBeInvalidated(billId, orgId, token);
    }

    @Override
    public PcwpRes<Boolean> checkOutBillCanBeInvalidatedForRevol(String billId, String orgId) {
        return pcwpThirdApiRevolRetailClient.checkOutBillCanBeInvalidated(billId, orgId, token);
    }

    @Override
    public PcwpRes<Void> saveAcceptanceForRevol(KeyedPayload<PcwpRevolAcceptanceDto> request) {
        return pcwpThirdApiRevolRetailClient.saveAcceptance(request, token,SYS_CODE_PCWP2);
    }

    @Override
    public PcwpRes<Void> rollBackSaveAcceptance(String keyId) {
        return pcwpThirdApiReconciliationClient.rollBackSaveAcceptance(keyId, token);
    }

    @Override
    public PcwpRes<String> savePlan(KeyedPayload<RetailPlan> plan) {
        return pcwpThirdApiRetailClient.savePlan(plan, token, SYS_CODE_PCWP2);
    }

    @Override
    public PcwpRes<Void> rollbackPlan(String keyId) {
        return pcwpThirdApiRetailClient.rollbackPlan(keyId, token, SYS_CODE_PCWP2);
    }

    @Override
    public PcwpRes<Void> updateRetailPlanDtl(KeyedPayload<List<UpdatePlanDtl>> payload) {
        return pcwpThirdApiRetailClient.updatePlanDtl(payload, token, SYS_CODE_PCWP2);
    }

    @Override
    public PcwpRes<SporadicPurchasePlanEx> getRetailPlanById(String id) {
        return pcwpThirdApiRetailClient.getRetailPlanById(id, token, SYS_CODE_PCWP2);
    }
    @Override
    public PcwpRes<PcwpPageRes<SporadicPurchasePlanQueryResult>> queryRetailPlansPage(SporadicPurchasePlanPageQueryCondition filter) {
        return pcwpThirdApiRetailClient.queryRetailPlansPage(filter, token, SYS_CODE_PCWP2);
    }

    @Override
    public PcwpRes<String> saveRevolRetailPlan(RevolPlan BulkRetailPlan) {
        return pcwpThirdApiRevolRetailClient.saveRevolRetailPlan(BulkRetailPlan, token, SYS_CODE_PCWP2);
    }

  
    @Override
    public PcwpRes<Void> rollbackRevolPlan(String keyId) {
        return pcwpThirdApiRevolRetailClient.rollbackRevolPlan(keyId, token, SYS_CODE_PCWP2);
    }

    public PcwpRes<PcwpPageRes<RevolPlanPageQueryResult>> queryPageRevolPlan(RevolPlanPageQueryCondition filter) {
        return pcwpThirdApiRevolRetailClient.queryPageRevolPlan(filter, token, SYS_CODE_PCWP2);
    };

    public PcwpRes<Void> updateRevolPlanDtl(KeyedPayload<List<UpdatePlanDtl>> payload) {
        return pcwpThirdApiRevolRetailClient.updatePlanDtl(payload, token, SYS_CODE_PCWP2);
    }

    @Override
    public PcwpRes<Map> getCanUseSiteReceivingDtl(PcwpAcceptanceRequest request) {
        return pcwpThirdApiSiteReceivingClient.getCanUseSiteReceivingDtl(request, token);
    }

    @Override
    public PcwpRes<Map> rollBackSaveSiteReceiving(String keyId) {
        return pcwpThirdApiSiteReceivingClient.rollBackSaveSiteReceiving(keyId, token);
    }

    @Override
    public PcwpRes<Map> rollBackWriteBackBillLockQuantiy(String keyId) {
        return pcwpThirdApiSiteReceivingClient.rollBackWriteBackBillLockQuantiy(keyId, token);
    }

    @Override
    public PcwpRes<Map> rollBackWriteBackBillQuantiy(String keyId) {
        return pcwpThirdApiSiteReceivingClient.rollBackWriteBackBillQuantiy(keyId, token);
    }

    @Override
    public PcwpRes<Map> saveSiteReceiving(PcwpSaveSiteReceivingRequest request) {
        return pcwpThirdApiSiteReceivingClient.saveSiteReceiving(request, token);
    }

    @Override
    public PcwpRes<Void> writeBackBillLockQuantiy(KeyedPayload<List<ReconciliationDtl>> payload) {
        return pcwpThirdApiSiteReceivingClient.writeBackBillLockQuantiy(payload, token);
    }

    @Override
    public PcwpRes<Void> writeBackBillQuantiy(KeyedPayload<List<ReconciliationDtl>> payload) {
        return pcwpThirdApiSiteReceivingClient.writeBackBillQuantiy(payload, token);
    }

    @Override
    public PcwpRes<Map<String, Object>> getPersonPermissons(@RequestBody Map<String,Object> params,
                                            @RequestHeader(value = "token") String token,
                                            @RequestHeader(value = "sysCode") String sysCode,
                                            @RequestHeader(value = "org") String org) {
        return pcwpThirdApiCloudCenterClient.getPersonPermissons(params, token, sysCode, org);
    }

    public PcwpRes<String> pushByShop(SupplierData supplierData) {
        return pcwpThirdApiClient.pushByShop(supplierData, token);
    }

    @Override
    public PcwpRes<StatePlan> getBulkRetailPlanById(String id) {
        return pcwpThirdApiBulkRetailClient.getBulkRetailPlanById(id, token, SYS_CODE_PCWP2);
    }

    @Override
    public PcwpRes<StatePlan> getRevolRetailPlanById(String id) {
        return pcwpThirdApiRevolRetailClient.getRevolRetailPlanById(id, token, SYS_CODE_PCWP2);
    }
    private static final MultipartFile[] EMPTY_FILE_ARRAY = new MultipartFile[]{};
    @Override
    public PcwpRes<Void> materialUploade(List<MultipartFile> files, String relationId, String shortCode) {
        return pcwpThirdApiUploadClient.materialUploade(files.toArray(EMPTY_FILE_ARRAY), relationId, bucketName, shortCode, token, SYS_CODE_PCWP2);
    }

}
