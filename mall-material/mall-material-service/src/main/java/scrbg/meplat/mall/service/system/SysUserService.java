package scrbg.meplat.mall.service.system;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.dto.system.UserPasswordDTO;
import scrbg.meplat.mall.entity.system.SysRole2;
import scrbg.meplat.mall.entity.system.SysUser;

import java.util.List;

public interface SysUserService {


    PageUtils<SysUser> queryPage(JSONObject jsonObject, LambdaQueryWrapper<SysUser> queryWrapper);

    void createUser(SysUser user);

    //同步用户
    void syncUser(SysUser user);

    void updateUser(SysUser user);


    SysUser getUserById(String id);

    void deleteUser(String id);

    void resetPassword(UserPasswordDTO passwordDTO);

    List<SysRole2> getRoleList();


}
