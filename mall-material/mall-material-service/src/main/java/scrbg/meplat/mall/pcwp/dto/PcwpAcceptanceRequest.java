package scrbg.meplat.mall.pcwp.dto;

import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

/**
 * PCWP验收单请求对象
 * <AUTHOR>
 * @date 2024
 */
@Data
public class PcwpAcceptanceRequest {
    
    /**
     * 验收数据
     */
    private PcwpAcceptanceData data;
    
    /**
     * 唯一标识
     */
    private String keyId;
    
    /**
     * PCWP验收单数据
     */
    @Data
    public static class PcwpAcceptanceData {
        
        // 基础信息
        /**
         * 验收总金额（不含税）
         */
        private BigDecimal acceptanceAmount;
        
        /**
         * 验收日期 (yyyy-MM-dd格式)
         */
        private String acceptanceDate;
        
        /**
         * 验收总数量
         */
        private BigDecimal acceptanceQuantity;
        
        /**
         * 验收人员ID
         */
        private String acceptancerId;
        
        /**
         * 验收人员姓名
         */
        private String acceptancerName;
        
        /**
         * 单据ID
         */
        private String billId;
        
        /**
         * 验收单据编号
         */
        private String billNo;
        
        /**
         * 业务类型
         */
        private Integer businessType;
        
        /**
         * 币种名称
         */
        private String currency;
        
        /**
         * 币种ID
         */
        private String currencyId;
        
        // 明细数据
        /**
         * 验收明细列表
         */
        private List<PcwpAcceptanceDtl> dtls;
        
        // 财务信息
        /**
         * 税额
         */
        private BigDecimal taxAmount;
        
        /**
         * 税率
         */
        private BigDecimal taxRate;
        
        /**
         * 税价合计（含税总金额）
         */
        private BigDecimal totalAmount;
        
        // 关联信息
        /**
         * 创建人ID
         */
        private String founderId;
        
        /**
         * 创建人姓名
         */
        private String founderName;
        
        /**
         * 运输单位费用
         */
        private BigDecimal freight;
        
        /**
         * 机构ID
         */
        private String orgId;
        
        /**
         * 机构名称
         */
        private String orgName;
        
        /**
         * 对账单ID
         */
        private String outerId;
        
        /**
         * 对账单编号
         */
        private String outerNo;
        
        /**
         * 采购人员ID
         */
        private String purchaserId;
        
        /**
         * 采购人员姓名
         */
        private String purchaserName;
        
        /**
         * 采购单位ID
         */
        private String purchasingUnitId;
        
        /**
         * 采购单位名称
         */
        private String purchasingUnitName;
        
        // 其他信息
        /**
         * 备注
         */
        private String remarks;
        
        /**
         * 源单ID
         */
        private String sourceBillId;
        
        /**
         * 源单名称
         */
        private String sourceBillName;

        /**
         * 源单编号
         */
        private String sourceBillNo;

        /**
         * 供应商ID
         */
        private String supplierId;

        /**
         * 供应商名称
         */
        private String supplierName;

        /**
         * 清单类型
         */
        private Integer type;
    }
    
    /**
     * PCWP验收明细
     */
    @Data
    public static class PcwpAcceptanceDtl {

        /**
         * 验收金额
         */
        private BigDecimal acceptanceAmount;

        /**
         * 验收数量
         */
        private BigDecimal acceptanceQuantity;

        /**
         * 单据ID
         */
        private String billId;

        /**
         * 明细ID
         */
        private String dtlId;

        /**
         * 出厂价
         */
        private BigDecimal factoryPrice;

        /**
         * 固定费用
         */
        private BigDecimal fixedFee;

        /**
         * 运费
         */
        private BigDecimal freight;

        /**
         * 物资类别ID
         */
        private String materialClassId;

        /**
         * 物资类别名称
         */
        private String materialClassName;

        /**
         * 物资ID
         */
        private String materialId;

        /**
         * 物资名称
         */
        private String materialName;

        /**
         * 网络价格
         */
        private BigDecimal networkPrice;

        /**
         * 未验收数量
         */
        private BigDecimal notAcceptedQuantity;

        /**
         * 订单ID
         */
        private String orderId;

        /**
         * 订单编号
         */
        private String orderNo;

        /**
         * 价格
         */
        private BigDecimal price;

        /**
         * 源单明细ID
         */
        private String sourceDtlId;

        /**
         * 规格型号
         */
        private String spec;

        /**
         * 税额
         */
        private BigDecimal taxAmount;

        /**
         * 材质
         */
        private String texture;

        /**
         * 贸易ID
         */
        private String tradeId;

        /**
         * 计量单位
         */
        private String unit;

        /**
         * 仓库ID
         */
        private String warehouseId;

        /**
         * 仓库名称
         */
        private String warehouseName;
    }
}
