package scrbg.meplat.mall.pcwp.mock;

import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;
import scrbg.meplat.mall.pcwp.KeyedPayload;
import scrbg.meplat.mall.pcwp.PcwpRes;
import scrbg.meplat.mall.pcwp.dto.PcwpAcceptanceRequest;
import scrbg.meplat.mall.pcwp.dto.PcwpSiteReceiptRequest;
import scrbg.meplat.mall.pcwp.dto.PcwpSaveSiteReceivingRequest;
import scrbg.meplat.mall.pcwp.third.PcwpThirdApiSiteReceivingClient;
import scrbg.meplat.mall.pcwp.third.model.ReconciliationDtl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * mock客户端 -第三方接口服务-现场收料
 */
@Component
@Profile("mock-pcwp")
@Primary
public class MockPcwpThirdApiSiteReceivingClient implements PcwpThirdApiSiteReceivingClient {

    @Override
    public PcwpRes<Map> getCanUseSiteReceivingDtl(PcwpAcceptanceRequest request, String token) {
        // 根据注释创建符合格式的Mock数据
        Map<String, Object> mockData = new HashMap<>();
        mockData.put("currPage", 1);
        mockData.put("pageSize", 10);
        mockData.put("totalCount", 1);
        mockData.put("totalPage", 1);

        // 创建Mock物资明细列表
        Map<String, Object> mockItem = new HashMap<>();
        mockItem.put("billId", "mock_bill_001");
        mockItem.put("billNo", "BILL-2024-001");
        mockItem.put("dtlId", "mock_dtl_001");
        mockItem.put("factoryPrice", 100.0);
        mockItem.put("fixedFee", 10.0);
        mockItem.put("freight", 5.0);
        mockItem.put("materialId", "mock_material_001");
        mockItem.put("materialName", "Mock物资");
        mockItem.put("quantity", 50.0);
        mockItem.put("totalAmount", 5000.0);
        mockItem.put("orgId", "mock_org_001");
        mockItem.put("orgName", "Mock机构");
        mockItem.put("supplierId", "mock_supplier_001");
        mockItem.put("supplierName", "Mock供应商");

        mockData.put("list", java.util.Arrays.asList(mockItem));

        return PcwpRes.<Map>builder().data(mockData).code(200).message("Mock success").build();
    }

    @Override
    public PcwpRes<Map> rollBackSaveSiteReceiving(String keyId, String token) {
        Map<String, Object> mockData = new HashMap<>();
        mockData.put("success", true);
        mockData.put("keyId", keyId);
        return PcwpRes.<Map>builder().data(mockData).code(200).message("Mock success").build();
    }

    @Override
    public PcwpRes<Map> rollBackWriteBackBillLockQuantiy(String keyId, String token) {
        Map<String, Object> mockData = new HashMap<>();
        mockData.put("success", true);
        mockData.put("keyId", keyId);
        return PcwpRes.<Map>builder().data(mockData).code(200).message("Mock success").build();
    }

    @Override
    public PcwpRes<Map> rollBackWriteBackBillQuantiy(String keyId, String token) {
        Map<String, Object> mockData = new HashMap<>();
        mockData.put("success", true);
        mockData.put("keyId", keyId);
        return PcwpRes.<Map>builder().data(mockData).code(200).message("Mock success").build();
    }

    @Override
    public PcwpRes<Map> saveSiteReceiving(PcwpSaveSiteReceivingRequest request, String token) {
        Map<String, Object> mockData = new HashMap<>();
        mockData.put("success", true);
        mockData.put("keyId", request.getKeyId());
        mockData.put("siteReceivingId", "mock_site_receiving_" + request.getKeyId());
        if (request.getData() != null) {
            mockData.put("orgId", request.getData().getOrgId());
            mockData.put("billNo", request.getData().getBillNo());
            mockData.put("detailsCount", request.getData().getDetails() != null ? request.getData().getDetails().size() : 0);
        }
        return PcwpRes.<Map>builder().data(mockData).code(200).message("Mock success").build();
    }

    @Override
    public PcwpRes<Void> writeBackBillLockQuantiy(KeyedPayload<List<ReconciliationDtl>> payload, String token) {
        Map<String, Object> mockData = new HashMap<>();
        mockData.put("success", true);
        mockData.put("keyId", payload.getKeyId());
        mockData.put("orgId", payload.getOrgId());
        mockData.put("processedCount", payload.getData() != null ? payload.getData().size() : 0);
        return PcwpRes.<Void>builder().code(200).build();
    }

    @Override
    public PcwpRes<Void> writeBackBillQuantiy(KeyedPayload<List<ReconciliationDtl>> payload, String token) {
        Map<String, Object> mockData = new HashMap<>();
        mockData.put("success", true);
        mockData.put("keyId", payload.getKeyId());
        mockData.put("orgId", payload.getOrgId());
        mockData.put("processedCount", payload.getData() != null ? payload.getData().size() : 0);
        return PcwpRes.<Void>builder().code(200).build();
    }
}
